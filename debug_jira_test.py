#!/usr/bin/env python3
"""
Debug script to understand Jira API issues.
"""

import sys
import os
import logging
import json

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from common.jira.jira_client import JiraClient

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def debug_jira_create_issue():
    """Debug the create_issue functionality."""
    
    base_url = "https://aiordinate.atlassian.net"
    username = "<EMAIL>"
    password = "ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC"
    
    print("=" * 60)
    print("DEBUGGING JIRA CREATE ISSUE")
    print("=" * 60)
    
    try:
        # Create JiraClient instance
        client = JiraClient(base_url, username, password)
        
        print("Testing create_issue...")
        response = client.create_issue(
            project_key="AA",
            summary="Debug Test Issue",
            description="This is a debug test to understand the API response",
            priority="Medium",
            test_type="Manual"
        )
        
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Text: {response.text}")
        
        if response.status_code == 201:
            try:
                response_json = response.json()
                print(f"Response JSON: {json.dumps(response_json, indent=2)}")
                if 'key' in response_json:
                    print(f"✅ Issue created successfully: {response_json['key']}")
                else:
                    print("❌ No 'key' field in response")
            except Exception as e:
                print(f"❌ Failed to parse JSON: {e}")
        else:
            print("❌ Issue creation failed")
            try:
                error_json = response.json()
                print(f"Error details: {json.dumps(error_json, indent=2)}")
            except:
                print("Could not parse error response as JSON")
                
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        logging.error(f"Debug test failed: {str(e)}", exc_info=True)

def debug_jira_auth():
    """Debug Jira authentication."""
    
    base_url = "https://aiordinate.atlassian.net"
    username = "<EMAIL>"
    password = "ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC"
    
    print("\n" + "=" * 60)
    print("DEBUGGING JIRA AUTHENTICATION")
    print("=" * 60)
    
    try:
        # Create JiraClient instance
        client = JiraClient(base_url, username, password)
        
        print("Testing get_jira_issue_details for AA-2...")
        response = client.get_jira_issue_details("AA-2")
        
        print(f"Response Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Authentication successful!")
            try:
                issue_data = response.json()
                print(f"Issue key: {issue_data.get('key', 'N/A')}")
                print(f"Issue summary: {issue_data.get('fields', {}).get('summary', 'N/A')}")
            except Exception as e:
                print(f"Failed to parse response: {e}")
        else:
            print("❌ Authentication or request failed")
            print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        logging.error(f"Auth debug failed: {str(e)}", exc_info=True)

if __name__ == "__main__":
    print("🔍 Starting Jira Debug Tests...")
    
    # Test 1: Authentication
    debug_jira_auth()
    
    # Test 2: Create Issue
    debug_jira_create_issue()
    
    print("\n" + "=" * 60)
    print("DEBUG COMPLETE")
    print("=" * 60)
