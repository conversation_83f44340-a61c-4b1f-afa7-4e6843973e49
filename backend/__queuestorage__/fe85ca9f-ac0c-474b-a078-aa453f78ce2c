{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"02643023-3ddb-440b-a8ad-eea89da4d651","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.ExecutionStartedEvent","OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"4b199339ae404ce2b340ba7e26f42097","ExecutionId":"b46ffae255114ed0a31bf47c49b4aa74"},"EventType":0,"ParentInstance":null,"Name":"DurableFunctionsOrchestrator","Version":"","Input":"\"{\\\"issue_type\\\": \\\"Manual\\\", \\\"input_type\\\": \\\"jira_id\\\", \\\"request_data\\\": \\\"{'Summary': 'Not Given-Develop Grafana Dashboard for Telemetry Data Visualization', 'Description': '', 'Title:** Not Given-Develop Grafana Dashboard for Telemetry Data Visualization   \\\\\\\\n\\\\\\\\n**Description:** \\\\\\\\n*As a* Data Analyst  \\\\\\\\n*I want* to create a Grafana dashboard to visualize telemetry data, including metrics like system performance, error rates, and usage trends  \\\\\\\\n*So that* I can monitor and analyze operational telemetry data effectively to identify issues and optimize performance.\\\\\\\\n\\\\\\\\n *Note: This is AI generated content* \\\\\\\\n\\\\\\\\n**Acceptance Criteria:** \\\\\\\\n**AC01: Grafana Dashboard Creation** \\\\\\\\n*Given* telemetry data is available in the database  \\\\\\\\n*When* a Grafana dashboard is created  \\\\\\\\n*Then* it should display the telemetry data using specific visualization types such as line charts for trends, bar graphs for comparisons, and tables for detailed metrics, with a maximum load time of 2 seconds per visualization.  \\\\\\\\n**AC02: Streamlit App Integration** \\\\\\\\n*Given* the Grafana dashboard is ready  \\\\\\\\n*When* the dashboard is embedded into the Streamlit app  \\\\\\\\n*Then* users should be able to interact with the dashboard, including filtering data by time range, exporting data, and drilling down into specific metrics without performance issues.  \\\\\\\\n**AC03: Real-Time Data Updates** \\\\\\\\n*Given* new telemetry data is added to the database  \\\\\\\\n*When* the Grafana dashboard is refreshed  \\\\\\\\n*Then* the Streamlit app should display the updated data within a refresh rate of 5 seconds, ensuring smooth real-time updates. \\\\\\\\n\\\\\\\\n**Priority:** High \\\\\\\\n\\\\\\\\n**Estimated Effort:** N/A\\\\\\\\n\\\\\\\\n **Acceptance Criteria': 'Not Given', 'parent': 'AA-2'}\\\", \\\"process_type\\\": \\\"get_data\\\", \\\"helper_name\\\": \\\"TCG\\\"}\"","Tags":null,"Correlation":null,"ParentTraceContext":null,"ScheduledStartTime":null,"Generation":0,"EventId":-1,"IsPlayed":false,"Timestamp":"2025-07-29T11:07:23.7769561Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"4b199339ae404ce2b340ba7e26f42097","ExecutionId":"b46ffae255114ed0a31bf47c49b4aa74"},"OrchestrationExecutionContext":null},"CompressedBlobName":null,"SequenceNumber":1,"Sender":{"InstanceId":"","ExecutionId":""},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"a564f4f9-e521-4a67-9536-df5975c9fb5c","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.TaskScheduledEvent","EventType":4,"Name":"ExecuteTCG","Version":"","Input":"[\"{\\\"issue_type\\\": \\\"Manual\\\", \\\"input_type\\\": \\\"jira_id\\\", \\\"request_data\\\": \\\"{'Summary': 'Not Given-Develop Grafana Dashboard for Telemetry Data Visualization', 'Description': '', 'Title:** Not Given-Develop Grafana Dashboard for Telemetry Data Visualization   \\\\\\\\n\\\\\\\\n**Description:** \\\\\\\\n*As a* Data Analyst  \\\\\\\\n*I want* to create a Grafana dashboard to visualize telemetry data, including metrics like system performance, error rates, and usage trends  \\\\\\\\n*So that* I can monitor and analyze operational telemetry data effectively to identify issues and optimize performance.\\\\\\\\n\\\\\\\\n *Note: This is AI generated content* \\\\\\\\n\\\\\\\\n**Acceptance Criteria:** \\\\\\\\n**AC01: Grafana Dashboard Creation** \\\\\\\\n*Given* telemetry data is available in the database  \\\\\\\\n*When* a Grafana dashboard is created  \\\\\\\\n*Then* it should display the telemetry data using specific visualization types such as line charts for trends, bar graphs for comparisons, and tables for detailed metrics, with a maximum load time of 2 seconds per visualization.  \\\\\\\\n**AC02: Streamlit App Integration** \\\\\\\\n*Given* the Grafana dashboard is ready  \\\\\\\\n*When* the dashboard is embedded into the Streamlit app  \\\\\\\\n*Then* users should be able to interact with the dashboard, including filtering data by time range, exporting data, and drilling down into specific metrics without performance issues.  \\\\\\\\n**AC03: Real-Time Data Updates** \\\\\\\\n*Given* new telemetry data is added to the database  \\\\\\\\n*When* the Grafana dashboard is refreshed  \\\\\\\\n*Then* the Streamlit app should display the updated data within a refresh rate of 5 seconds, ensuring smooth real-time updates. \\\\\\\\n\\\\\\\\n**Priority:** High \\\\\\\\n\\\\\\\\n**Estimated Effort:** N/A\\\\\\\\n\\\\\\\\n **Acceptance Criteria': 'Not Given', 'parent': 'AA-2'}\\\", \\\"process_type\\\": \\\"get_data\\\", \\\"helper_name\\\": \\\"TCG\\\"}\"]","ParentTraceContext":null,"EventId":0,"IsPlayed":false,"Timestamp":"2025-07-29T11:07:24.4348707Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"4b199339ae404ce2b340ba7e26f42097","ExecutionId":"b46ffae255114ed0a31bf47c49b4aa74"},"OrchestrationExecutionContext":{"$type":"DurableTask.Core.OrchestrationExecutionContext","OrchestrationTags":{"$type":"System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib],[System.String, System.Private.CoreLib]]"}}},"CompressedBlobName":null,"SequenceNumber":2,"Episode":1,"Sender":{"InstanceId":"4b199339ae404ce2b340ba7e26f42097","ExecutionId":"b46ffae255114ed0a31bf47c49b4aa74"},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"00000000-0000-0000-0000-000000000000","TaskMessage":null,"CompressedBlobName":"4b199339ae404ce2b340ba7e26f42097/message-c99e28ec648145c4b398264334368f74-TaskCompleted.json.gz","SequenceNumber":0,"Sender":null,"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"d7c15f1d-3f0a-4e45-964d-35b169473e67","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.ExecutionStartedEvent","OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"42059b4d5ec943b197f1c590076b5a32","ExecutionId":"3ed775cca4c6465c97f1423c59765728"},"EventType":0,"ParentInstance":null,"Name":"DurableFunctionsOrchestrator","Version":"","Input":"\"{\\\"issue_type\\\": \\\"Manual\\\", \\\"input_type\\\": \\\"jira_id\\\", \\\"request_data\\\": \\\"TestCaseID:AA-2 - TC 01\\\\nSummary:AA-2 - TC 01 - Verify Grafana dashboard displays telemetry data with specific visualization types\\\\nDescription:Ensure that the Grafana dashboard displays telemetry data using line charts for trends, bar graphs for comparisons, and tables for detailed metrics, with a maximum load time of 2 seconds per visualization.\\\\nManualSteps: \\\\n    Action: Access the Grafana dashboard creation interface.\\\\n    Data: N/A\\\\n    Expected Result: Grafana dashboard creation interface should be accessible.\\\\n    Action: Connect the dashboard to the telemetry data source in the database.\\\\n    Data: Database connection details\\\\n    Expected Result: Dashboard should successfully connect to the telemetry data source.\\\\n    Action: Create visualizations such as line charts, bar graphs, and tables for the telemetry data.\\\\n    Data: Telemetry data metrics\\\\n    Expected Result: Visualizations should be created and display the telemetry data correctly.\\\\n    Action: Verify the load time for each visualization using a performance monitoring tool.\\\\n    Data: N/A\\\\n    Expected Result: Load time should not exceed 2 seconds per visualization.\\\\n    Action: Attempt to load a visualization with a large volume of telemetry data.\\\\n    Data: Telemetry data exceeding typical size limits\\\\n    Expected Result: Dashboard should handle large data volumes without performance degradation.\\\\n    Action: Simulate a failure in connecting to the telemetry data source.\\\\n    Data: Invalid database connection details\\\\n    Expected Result: Dashboard should display an error message indicating connection failure.\\\\nPriority:High\\\\n\\\\n ********** \\\\nTestCaseID:AA-2 - TC 02\\\\nSummary:AA-2 - TC 02 - Validate Grafana dashboard integration into Streamlit app\\\\nDescription:Ensure that the Grafana dashboard is embedded into the Streamlit app and users can interact with the dashboard without performance issues.\\\\nManualSteps: \\\\n    Action: Embed the Grafana dashboard into the Streamlit app.\\\\n    Data: Grafana dashboard URL or embed code\\\\n    Expected Result: Dashboard should be embedded successfully into the Streamlit app.\\\\n    Action: Interact with the dashboard by filtering data by time range.\\\\n    Data: Time range filter criteria\\\\n    Expected Result: Dashboard should filter data based on the selected time range.\\\\n    Action: Attempt to apply invalid filter criteria.\\\\n    Data: Unsupported time range or metric\\\\n    Expected Result: Dashboard should display an error message indicating invalid filter criteria.\\\\n    Action: Export data from the dashboard.\\\\n    Data: Export options\\\\n    Expected Result: Data should be exported successfully.\\\\n    Action: Drill down into specific metrics using the dashboard.\\\\n    Data: Metric selection criteria\\\\n    Expected Result: Dashboard should display detailed metrics without performance issues.\\\\nPriority:High\\\\n\\\\n ********** \\\\nTestCaseID:AA-2 - TC 03\\\\nSummary:AA-2 - TC 03 - Verify real-time data updates on Grafana dashboard and Streamlit app\\\\nDescription:Ensure that new telemetry data added to the database is reflected on the Grafana dashboard and Streamlit app within a refresh rate of 5 seconds.\\\\nManualSteps: \\\\n    Action: Add new telemetry data to the database.\\\\n    Data: New telemetry data\\\\n    Expected Result: Data should be added to the database successfully.\\\\n    Action: Refresh the Grafana dashboard and measure refresh rate using a timer.\\\\n    Data: N/A\\\\n    Expected Result: Dashboard should display the updated telemetry data within 5 seconds.\\\\n    Action: Verify the Streamlit app displays the updated data.\\\\n    Data: N/A\\\\n    Expected Result: Streamlit app should display the updated data within a refresh rate of 5 seconds.\\\\n    Action: Simulate a delay in database updates.\\\\n    Data: Telemetry data with delayed insertion\\\\n    Expected Result: Dashboard should handle delayed updates gracefully and display data as soon as available.\\\\n    Action: Attempt to refresh the dashboard with invalid telemetry data.\\\\n    Data: Corrupted or incomplete telemetry data\\\\n    Expected Result: Dashboard should display an error message indicating data issues.\\\\nPriority:High\\\\n\\\", \\\"process_type\\\": \\\"push_data\\\", \\\"base_url\\\": \\\"https://aiordinate.atlassian.net/\\\", \\\"proj_key\\\": \\\"\\\", \\\"jira_id\\\": \\\"AA-2\\\", \\\"activity\\\": \\\"PushtoJira\\\", \\\"jira_username\\\": \\\"<EMAIL>\\\", \\\"jira_password\\\": \\\"ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC\\\", \\\"helper_name\\\": \\\"TCG\\\"}\"","Tags":null,"Correlation":null,"ParentTraceContext":null,"ScheduledStartTime":null,"Generation":0,"EventId":-1,"IsPlayed":false,"Timestamp":"2025-07-29T11:10:03.8590645Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"42059b4d5ec943b197f1c590076b5a32","ExecutionId":"3ed775cca4c6465c97f1423c59765728"},"OrchestrationExecutionContext":null},"CompressedBlobName":null,"SequenceNumber":4,"Sender":{"InstanceId":"","ExecutionId":""},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"0af61a04-4cce-435a-86c3-bce2fcde2779","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.TaskScheduledEvent","EventType":4,"Name":"PushtoJira","Version":"","Input":"[\"{\\\"issue_type\\\": \\\"Manual\\\", \\\"input_type\\\": \\\"jira_id\\\", \\\"request_data\\\": \\\"TestCaseID:AA-2 - TC 01\\\\nSummary:AA-2 - TC 01 - Verify Grafana dashboard displays telemetry data with specific visualization types\\\\nDescription:Ensure that the Grafana dashboard displays telemetry data using line charts for trends, bar graphs for comparisons, and tables for detailed metrics, with a maximum load time of 2 seconds per visualization.\\\\nManualSteps: \\\\n    Action: Access the Grafana dashboard creation interface.\\\\n    Data: N/A\\\\n    Expected Result: Grafana dashboard creation interface should be accessible.\\\\n    Action: Connect the dashboard to the telemetry data source in the database.\\\\n    Data: Database connection details\\\\n    Expected Result: Dashboard should successfully connect to the telemetry data source.\\\\n    Action: Create visualizations such as line charts, bar graphs, and tables for the telemetry data.\\\\n    Data: Telemetry data metrics\\\\n    Expected Result: Visualizations should be created and display the telemetry data correctly.\\\\n    Action: Verify the load time for each visualization using a performance monitoring tool.\\\\n    Data: N/A\\\\n    Expected Result: Load time should not exceed 2 seconds per visualization.\\\\n    Action: Attempt to load a visualization with a large volume of telemetry data.\\\\n    Data: Telemetry data exceeding typical size limits\\\\n    Expected Result: Dashboard should handle large data volumes without performance degradation.\\\\n    Action: Simulate a failure in connecting to the telemetry data source.\\\\n    Data: Invalid database connection details\\\\n    Expected Result: Dashboard should display an error message indicating connection failure.\\\\nPriority:High\\\\n\\\\n ********** \\\\nTestCaseID:AA-2 - TC 02\\\\nSummary:AA-2 - TC 02 - Validate Grafana dashboard integration into Streamlit app\\\\nDescription:Ensure that the Grafana dashboard is embedded into the Streamlit app and users can interact with the dashboard without performance issues.\\\\nManualSteps: \\\\n    Action: Embed the Grafana dashboard into the Streamlit app.\\\\n    Data: Grafana dashboard URL or embed code\\\\n    Expected Result: Dashboard should be embedded successfully into the Streamlit app.\\\\n    Action: Interact with the dashboard by filtering data by time range.\\\\n    Data: Time range filter criteria\\\\n    Expected Result: Dashboard should filter data based on the selected time range.\\\\n    Action: Attempt to apply invalid filter criteria.\\\\n    Data: Unsupported time range or metric\\\\n    Expected Result: Dashboard should display an error message indicating invalid filter criteria.\\\\n    Action: Export data from the dashboard.\\\\n    Data: Export options\\\\n    Expected Result: Data should be exported successfully.\\\\n    Action: Drill down into specific metrics using the dashboard.\\\\n    Data: Metric selection criteria\\\\n    Expected Result: Dashboard should display detailed metrics without performance issues.\\\\nPriority:High\\\\n\\\\n ********** \\\\nTestCaseID:AA-2 - TC 03\\\\nSummary:AA-2 - TC 03 - Verify real-time data updates on Grafana dashboard and Streamlit app\\\\nDescription:Ensure that new telemetry data added to the database is reflected on the Grafana dashboard and Streamlit app within a refresh rate of 5 seconds.\\\\nManualSteps: \\\\n    Action: Add new telemetry data to the database.\\\\n    Data: New telemetry data\\\\n    Expected Result: Data should be added to the database successfully.\\\\n    Action: Refresh the Grafana dashboard and measure refresh rate using a timer.\\\\n    Data: N/A\\\\n    Expected Result: Dashboard should display the updated telemetry data within 5 seconds.\\\\n    Action: Verify the Streamlit app displays the updated data.\\\\n    Data: N/A\\\\n    Expected Result: Streamlit app should display the updated data within a refresh rate of 5 seconds.\\\\n    Action: Simulate a delay in database updates.\\\\n    Data: Telemetry data with delayed insertion\\\\n    Expected Result: Dashboard should handle delayed updates gracefully and display data as soon as available.\\\\n    Action: Attempt to refresh the dashboard with invalid telemetry data.\\\\n    Data: Corrupted or incomplete telemetry data\\\\n    Expected Result: Dashboard should display an error message indicating data issues.\\\\nPriority:High\\\\n\\\", \\\"process_type\\\": \\\"push_data\\\", \\\"base_url\\\": \\\"https://aiordinate.atlassian.net/\\\", \\\"proj_key\\\": \\\"\\\", \\\"jira_id\\\": \\\"AA-2\\\", \\\"activity\\\": \\\"PushtoJira\\\", \\\"jira_username\\\": \\\"<EMAIL>\\\", \\\"jira_password\\\": \\\"ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC\\\", \\\"helper_name\\\": \\\"TCG\\\"}\"]","ParentTraceContext":null,"EventId":0,"IsPlayed":false,"Timestamp":"2025-07-29T11:10:04.037922Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"42059b4d5ec943b197f1c590076b5a32","ExecutionId":"3ed775cca4c6465c97f1423c59765728"},"OrchestrationExecutionContext":{"$type":"DurableTask.Core.OrchestrationExecutionContext","OrchestrationTags":{"$type":"System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib],[System.String, System.Private.CoreLib]]"}}},"CompressedBlobName":null,"SequenceNumber":5,"Episode":1,"Sender":{"InstanceId":"42059b4d5ec943b197f1c590076b5a32","ExecutionId":"3ed775cca4c6465c97f1423c59765728"},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"464d1466-a295-463c-a505-454f222388ab","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.TaskCompletedEvent","EventType":5,"TaskScheduledId":0,"Result":"[400,\"Failed to create test issue: 400 - {\\\"errorMessages\\\":[],\\\"errors\\\":{\\\"project\\\":\\\"valid project is required\\\"}}\\nFailed to create test issue: 400 - {\\\"errorMessages\\\":[],\\\"errors\\\":{\\\"project\\\":\\\"valid project is required\\\"}}\\nFailed to create test issue: 400 - {\\\"errorMessages\\\":[],\\\"errors\\\":{\\\"project\\\":\\\"valid project is required\\\"}}\\n\"]","EventId":-1,"IsPlayed":false,"Timestamp":"2025-07-29T11:10:15.5102702Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"42059b4d5ec943b197f1c590076b5a32","ExecutionId":"3ed775cca4c6465c97f1423c59765728"},"OrchestrationExecutionContext":null},"CompressedBlobName":null,"SequenceNumber":6,"Episode":1,"Sender":{"InstanceId":"42059b4d5ec943b197f1c590076b5a32","ExecutionId":"3ed775cca4c6465c97f1423c59765728"},"SerializableTraceContext":null}