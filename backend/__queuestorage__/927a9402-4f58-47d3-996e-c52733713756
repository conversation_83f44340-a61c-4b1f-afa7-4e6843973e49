{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"aa0211ef-0f65-4d6d-ba82-e78a1a2b3566","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.ExecutionStartedEvent","OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"33db0de7814b44dc9c85580ac556b783","ExecutionId":"9c700338bd1f4c21b37875eb8e0789fe"},"EventType":0,"ParentInstance":null,"Name":"DurableFunctionsOrchestrator","Version":"","Input":"\"{\\\"helper_name\\\": \\\"RAS\\\", \\\"requirement\\\": \\\"**Summary**: Alternative Implementation Test \\\\n\\\\n **Description**:\\\\n\\\\n **Title:** Alternative Implementation Test\\\\n\\\\n**Description:**\\\\nTesting the Basic Auth implementation as an alternative to session-based authentication.\\\\n\\\\n**Acceptance Criteria:**\\\\n- Given Basic Auth credentials, when an update is made, then it should work reliably\\\\n- Given proper error handling, when issues occur, then clear messages should be returned\\\\n\\\\n**Priority:** Medium\\\\n\\\\n **Acceptance Criteria**:\\\\n\\\\n Not Given\\\"}\"","Tags":null,"Correlation":null,"ParentTraceContext":null,"ScheduledStartTime":null,"Generation":0,"EventId":-1,"IsPlayed":false,"Timestamp":"2025-07-29T08:49:09.4844305Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"33db0de7814b44dc9c85580ac556b783","ExecutionId":"9c700338bd1f4c21b37875eb8e0789fe"},"OrchestrationExecutionContext":null},"CompressedBlobName":null,"SequenceNumber":1,"Sender":{"InstanceId":"","ExecutionId":""},"SerializableTraceContext":null}{"$type":"DurableTask.AzureStorage.MessageData","ActivityId":"c9a55ff0-6ab7-493a-9c4b-1bee42a8f0d7","TaskMessage":{"$type":"DurableTask.Core.TaskMessage","Event":{"$type":"DurableTask.Core.History.TaskScheduledEvent","EventType":4,"Name":"ExecuteRAS","Version":"","Input":"[\"{\\\"helper_name\\\": \\\"RAS\\\", \\\"requirement\\\": \\\"**Summary**: Alternative Implementation Test \\\\n\\\\n **Description**:\\\\n\\\\n **Title:** Alternative Implementation Test\\\\n\\\\n**Description:**\\\\nTesting the Basic Auth implementation as an alternative to session-based authentication.\\\\n\\\\n**Acceptance Criteria:**\\\\n- Given Basic Auth credentials, when an update is made, then it should work reliably\\\\n- Given proper error handling, when issues occur, then clear messages should be returned\\\\n\\\\n**Priority:** Medium\\\\n\\\\n **Acceptance Criteria**:\\\\n\\\\n Not Given\\\"}\"]","ParentTraceContext":null,"EventId":0,"IsPlayed":false,"Timestamp":"2025-07-29T08:49:17.9027672Z"},"SequenceNumber":0,"OrchestrationInstance":{"$type":"DurableTask.Core.OrchestrationInstance","InstanceId":"33db0de7814b44dc9c85580ac556b783","ExecutionId":"9c700338bd1f4c21b37875eb8e0789fe"},"OrchestrationExecutionContext":{"$type":"DurableTask.Core.OrchestrationExecutionContext","OrchestrationTags":{"$type":"System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib],[System.String, System.Private.CoreLib]]"}}},"CompressedBlobName":null,"SequenceNumber":2,"Episode":1,"Sender":{"InstanceId":"33db0de7814b44dc9c85580ac556b783","ExecutionId":"9c700338bd1f4c21b37875eb8e0789fe"},"SerializableTraceContext":null}