{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "python", "POSTGRES_HOST": "sdlc-test.postgres.database.azure.com", "POSTGRES_PORT": "5432", "POSTGRES_DB": "postgres", "POSTGRES_USER": "yash", "POSTGRES_PASSWORD": "ai123@ordinate", "AZURE_SEARCH_ENDPOINT": "https://sdlc-test-ai-search.search.windows.net", "AZURE_SEARCH_ADMIN_KEY": "****************************************************", "STORAGE_ACCOUNT_NAME": "sdlctest", "STORAGE_ACCOUNT_KEY": "****************************************************************************************", "AZURE_CHAT_MODEL_NAME": "gpt-4o", "AZURE_OPENAI_API_VERSION": "2024-02-01", "AZURE_MODEL_DEPLOYMENT_NAME": "gpt-4o", "AZURE_OPENAI_ENDPOINT": "https://aio-az-openai-service1.openai.azure.com/", "AZURE_OPENAI_TEMPERATURE": "0.3", "AZURE_EMBEDDING": "text-embedding-ada-002", "AZURE_OPENAI_API_KEY": "AickLUhT2lIy1o2jsBPQIJgAuANCvrCSGhaZ7B28BEi3AokeoEslJQQJ99BEACHYHv6XJ3w3AAAAACOGzRJL"}}