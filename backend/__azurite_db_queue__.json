{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_queue__.json", "collections": [{"name": "$SERVICES_COLLECTION$", "data": [], "idIndex": null, "binaryIndices": {}, "constraints": null, "uniqueNames": ["accountName"], "transforms": {}, "objType": "$SERVICES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$QUEUES_COLLECTION$", "data": [{"accountName": "devstoreaccount1", "name": "durablefunctionshub-workitems", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 1}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-control-02", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 2}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-control-00", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 3}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-control-03", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 4}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-control-01", "metadata": {}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 5}], "idIndex": null, "binaryIndices": {"accountName": {"name": "accountName", "dirty": false, "values": [4, 3, 2, 1, 0]}, "name": {"name": "name", "dirty": false, "values": [2, 4, 1, 3, 0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$QUEUES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 5, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$MESSAGES_COLLECTION$", "data": [{"accountName": "devstoreaccount1", "queueName": "durablefunctionshub-workitems", "messageId": "54515dd8-d68f-4c0b-8069-812f5f7745a7", "popReceipt": "MjlKdWwyMDI1MDg6NTE6MThiNzU0", "timeNextVisible": *************, "persistency": {"id": "927a9402-4f58-47d3-996e-c52733713756", "offset": 1626, "count": 1623}, "insertionTime": "2025-07-29T08:49:17.945Z", "expirationTime": "9999-12-31T23:59:59.999Z", "dequeueCount": 1, "meta": {"revision": 5, "created": *************, "version": 0, "updated": *************}, "$loki": 47}], "idIndex": [47], "binaryIndices": {"accountName": {"name": "accountName", "dirty": false, "values": [0]}, "queueName": {"name": "queueName", "dirty": false, "values": [0]}, "messageId": {"name": "messageId", "dirty": false, "values": [0]}, "visibleTime": {"name": "visibleTime", "dirty": false, "values": [0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$MESSAGES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 47, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}