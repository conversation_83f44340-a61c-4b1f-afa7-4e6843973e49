{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_queue_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "58534f94-c6c8-41cf-974a-da8661a18bc3", "locationId": "<PERSON><PERSON><PERSON>", "path": "58534f94-c6c8-41cf-974a-da8661a18bc3", "size": 87228, "lastModifiedInMS": 1753780741168, "meta": {"revision": 18, "created": 1753780741168, "version": 0, "updated": 1753781832394}, "$loki": 6, "LastModifyInMS": 1753781832394}], "idIndex": [6], "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 6, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}