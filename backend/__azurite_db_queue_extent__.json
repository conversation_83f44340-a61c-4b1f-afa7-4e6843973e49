{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_queue_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "fe85ca9f-ac0c-474b-a078-aa453f78ce2c", "locationId": "<PERSON><PERSON><PERSON>", "path": "fe85ca9f-ac0c-474b-a078-aa453f78ce2c", "size": 19442, "lastModifiedInMS": 1753787244158, "meta": {"revision": 5, "created": 1753787244158, "version": 0, "updated": 1753787415726}, "$loki": 8, "LastModifyInMS": 1753787415726}], "idIndex": [8], "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 8, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}