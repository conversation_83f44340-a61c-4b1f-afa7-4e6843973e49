{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_queue_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "927a9402-4f58-47d3-996e-c52733713756", "locationId": "<PERSON><PERSON><PERSON>", "path": "927a9402-4f58-47d3-996e-c52733713756", "size": 3249, "lastModifiedInMS": 1753778950573, "meta": {"revision": 1, "created": 1753778950573, "version": 0, "updated": 1753778958308}, "$loki": 5, "LastModifyInMS": 1753778958307}], "idIndex": [5], "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 5, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}