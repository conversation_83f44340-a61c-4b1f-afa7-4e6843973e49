{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_blob__.json", "collections": [{"name": "$SERVICES_COLLECTION$", "data": [], "idIndex": null, "binaryIndices": {}, "constraints": null, "uniqueNames": ["accountName"], "transforms": {}, "objType": "$SERVICES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$CONTAINERS_COLLECTION$", "data": [{"accountName": "devstoreaccount1", "name": "azure-webjobs-secrets", "properties": {"etag": "\"0x25DDB7C2AE97660\"", "lastModified": "2025-07-16T04:37:23.429Z", "leaseStatus": "unlocked", "leaseState": "available", "hasImmutabilityPolicy": false, "hasLegalHold": false}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 1}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-applease", "properties": {"etag": "\"0x21527BCFDEB0F00\"", "lastModified": "2025-07-16T04:37:25.300Z", "leaseStatus": "locked", "leaseState": "leased", "hasImmutabilityPolicy": false, "hasLegalHold": false, "leaseDuration": "fixed"}, "meta": {"revision": 374, "created": *************, "version": 0, "updated": *************}, "$loki": 2, "leaseId": "811c9dc5-0000-0000-0000-************", "leaseExpireTime": "2025-07-29T09:46:44.507Z", "leaseDurationSeconds": 60}, {"accountName": "devstoreaccount1", "name": "azure-webjobs-hosts", "properties": {"etag": "\"0x26E5A09A31CCF60\"", "lastModified": "2025-07-16T04:37:29.840Z", "leaseStatus": "unlocked", "leaseState": "available", "hasImmutabilityPolicy": false, "hasLegalHold": false}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 3}, {"accountName": "devstoreaccount1", "name": "durablefunctionshub-largemessages", "properties": {"etag": "\"0x23002CF9AF549C0\"", "lastModified": "2025-07-16T04:52:51.136Z", "leaseStatus": "unlocked", "leaseState": "available", "hasImmutabilityPolicy": false, "hasLegalHold": false}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 4}], "idIndex": [1, 2, 3, 4], "binaryIndices": {"accountName": {"name": "accountName", "dirty": false, "values": [1, 3, 2, 0]}, "name": {"name": "name", "dirty": false, "values": [2, 0, 1, 3]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$CONTAINERS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 4, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$BLOBS_COLLECTION$", "data": [{"deleted": false, "accountName": "devstoreaccount1", "containerName": "azure-webjobs-secrets", "name": "yss-*********/host.json", "properties": {"creationTime": "2025-07-16T04:37:24.381Z", "lastModified": "2025-07-16T04:37:24.381Z", "etag": "\"0x26BE2CE677D54E0\"", "contentLength": 610, "contentType": "application/octet-stream", "contentMD5": {"type": "<PERSON><PERSON><PERSON>", "data": [15, 156, 8, 217, 243, 54, 9, 116, 85, 174, 73, 132, 77, 242, 117, 218]}, "blobType": "BlockBlob", "leaseStatus": "unlocked", "leaseState": "available", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-16T04:37:24.381Z"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 456, "count": 610}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 2}, {"deleted": false, "accountName": "devstoreaccount1", "containerName": "durablefunctionshub-applease", "name": "durablefunctionshub-appleaseinfo", "properties": {"creationTime": "2025-07-16T04:37:26.062Z", "lastModified": "2025-07-16T04:37:26.062Z", "etag": "\"0x249DD186F1AFA20\"", "contentLength": 71, "contentType": "application/octet-stream", "contentMD5": {"type": "<PERSON><PERSON><PERSON>", "data": [219, 76, 3, 245, 53, 70, 139, 220, 247, 201, 80, 127, 178, 151, 74, 135]}, "blobType": "BlockBlob", "leaseStatus": "unlocked", "leaseState": "available", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-16T04:37:26.062Z"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 1068, "count": 71}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 4}, {"deleted": false, "metadata": {"FunctionInstance": "************************000C4922"}, "accountName": "devstoreaccount1", "containerName": "azure-webjobs-hosts", "name": "locks/yss-*********/host", "properties": {"creationTime": "2025-07-16T04:37:29.850Z", "lastModified": "2025-07-29T09:17:21.654Z", "etag": "\"0x1C1E944D337A360\"", "contentLength": 0, "contentType": "application/octet-stream", "contentMD5": {"type": "<PERSON><PERSON><PERSON>", "data": [212, 29, 140, 217, 143, 0, 178, 4, 233, 128, 9, 152, 236, 248, 66, 126]}, "blobType": "BlockBlob", "leaseStatus": "locked", "leaseState": "leased", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-16T04:37:29.850Z", "leaseDuration": "fixed"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 1139, "count": 0}, "meta": {"revision": 755, "created": *************, "version": 0, "updated": *************}, "$loki": 5, "leaseId": "************************000C4922", "leaseExpireTime": "2025-07-29T09:46:12.684Z", "leaseDurationSeconds": 15}, {"deleted": false, "accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "name": "2528cecc906046389e5c0898e01f1394/message-bf14ba7749eb41aa929c73b616a5f240-TaskCompleted.json.gz", "properties": {"creationTime": "2025-07-16T04:52:51.449Z", "lastModified": "2025-07-16T04:52:53.402Z", "etag": "\"0x1E11E99A823B0C0\"", "contentLength": 5679, "contentType": "application/octet-stream", "blobType": "BlockBlob", "leaseStatus": "unlocked", "leaseState": "available", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-16T04:52:51.449Z"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 1139, "count": 0}, "meta": {"revision": 3, "created": *************, "version": 0, "updated": *************}, "$loki": 6, "committedBlocksInOrder": [{"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "2528cecc906046389e5c0898e01f1394/message-bf14ba7749eb41aa929c73b616a5f240-TaskCompleted.json.gz", "isCommitted": false, "name": "JRYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 5669, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 1139, "count": 5669}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 1}, {"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "2528cecc906046389e5c0898e01f1394/message-bf14ba7749eb41aa929c73b616a5f240-TaskCompleted.json.gz", "isCommitted": false, "name": "LxYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 10, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 6808, "count": 10}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 2}]}, {"deleted": false, "accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "name": "2528cecc906046389e5c0898e01f1394/history-****************-TaskCompleted-13D793A0-Result.json.gz", "properties": {"creationTime": "2025-07-16T04:52:54.521Z", "lastModified": "2025-07-16T04:52:55.040Z", "etag": "\"0x23F191E0FE6ECE0\"", "contentLength": 5213, "contentType": "application/octet-stream", "blobType": "BlockBlob", "leaseStatus": "unlocked", "leaseState": "available", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-16T04:52:54.521Z"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 6818, "count": 0}, "meta": {"revision": 3, "created": *************, "version": 0, "updated": *************}, "$loki": 7, "committedBlocksInOrder": [{"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "2528cecc906046389e5c0898e01f1394/history-****************-TaskCompleted-13D793A0-Result.json.gz", "isCommitted": false, "name": "UxQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 5203, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 6818, "count": 5203}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 3}, {"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "2528cecc906046389e5c0898e01f1394/history-****************-TaskCompleted-13D793A0-Result.json.gz", "isCommitted": false, "name": "XRQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 10, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 12021, "count": 10}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 4}]}, {"deleted": false, "accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "name": "2528cecc906046389e5c0898e01f1394/history-****************-ExecutionCompleted-6F887B7B-Result.json.gz", "properties": {"creationTime": "2025-07-16T04:52:55.082Z", "lastModified": "2025-07-16T04:52:55.614Z", "etag": "\"0x1CC2C52B3F00770\"", "contentLength": 5213, "contentType": "application/octet-stream", "blobType": "BlockBlob", "leaseStatus": "unlocked", "leaseState": "available", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-16T04:52:55.082Z"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 12031, "count": 0}, "meta": {"revision": 3, "created": *************, "version": 0, "updated": *************}, "$loki": 8, "committedBlocksInOrder": [{"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "2528cecc906046389e5c0898e01f1394/history-****************-ExecutionCompleted-6F887B7B-Result.json.gz", "isCommitted": false, "name": "UxQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 5203, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 12031, "count": 5203}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 5}, {"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "2528cecc906046389e5c0898e01f1394/history-****************-ExecutionCompleted-6F887B7B-Result.json.gz", "isCommitted": false, "name": "XRQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 10, "persistency": {"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "offset": 17234, "count": 10}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 6}]}, {"deleted": false, "accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "name": "328b32a338ab4f25bc1fdd10d14f941e/message-10f7494d5bbd460d91f1ef17676206d0-TaskCompleted.json.gz", "properties": {"creationTime": "2025-07-29T09:34:55.244Z", "lastModified": "2025-07-29T09:34:58.754Z", "etag": "\"0x201173297B60160\"", "contentLength": 5245, "contentType": "application/octet-stream", "blobType": "BlockBlob", "leaseStatus": "unlocked", "leaseState": "available", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-29T09:34:55.244Z"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 2, "count": 0}, "meta": {"revision": 3, "created": *************, "version": 0, "updated": *************}, "$loki": 9, "committedBlocksInOrder": [{"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "328b32a338ab4f25bc1fdd10d14f941e/message-10f7494d5bbd460d91f1ef17676206d0-TaskCompleted.json.gz", "isCommitted": false, "name": "cxQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 5235, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 2, "count": 5235}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 7}, {"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "328b32a338ab4f25bc1fdd10d14f941e/message-10f7494d5bbd460d91f1ef17676206d0-TaskCompleted.json.gz", "isCommitted": false, "name": "fRQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 10, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 5237, "count": 10}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 8}]}, {"deleted": false, "accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "name": "328b32a338ab4f25bc1fdd10d14f941e/history-****************-TaskCompleted-3083356A-Result.json.gz", "properties": {"creationTime": "2025-07-29T09:35:01.214Z", "lastModified": "2025-07-29T09:35:02.083Z", "etag": "\"0x21F80A982072F20\"", "contentLength": 4746, "contentType": "application/octet-stream", "blobType": "BlockBlob", "leaseStatus": "unlocked", "leaseState": "available", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-29T09:35:01.214Z"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 5247, "count": 0}, "meta": {"revision": 3, "created": *************, "version": 0, "updated": *************}, "$loki": 10, "committedBlocksInOrder": [{"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "328b32a338ab4f25bc1fdd10d14f941e/history-****************-TaskCompleted-3083356A-Result.json.gz", "isCommitted": false, "name": "gBIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 4736, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 5247, "count": 4736}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 9}, {"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "328b32a338ab4f25bc1fdd10d14f941e/history-****************-TaskCompleted-3083356A-Result.json.gz", "isCommitted": false, "name": "ihIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 10, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 9983, "count": 10}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 10}]}, {"deleted": false, "accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "name": "328b32a338ab4f25bc1fdd10d14f941e/history-****************-ExecutionCompleted-7130A235-Result.json.gz", "properties": {"creationTime": "2025-07-29T09:35:02.104Z", "lastModified": "2025-07-29T09:35:03.582Z", "etag": "\"0x237A24F98A32400\"", "contentLength": 4746, "contentType": "application/octet-stream", "blobType": "BlockBlob", "leaseStatus": "unlocked", "leaseState": "available", "serverEncrypted": true, "accessTier": "Hot", "accessTierInferred": true, "accessTierChangeTime": "2025-07-29T09:35:02.104Z"}, "snapshot": "", "isCommitted": true, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 9993, "count": 0}, "meta": {"revision": 3, "created": *************, "version": 0, "updated": *************}, "$loki": 11, "committedBlocksInOrder": [{"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "328b32a338ab4f25bc1fdd10d14f941e/history-****************-ExecutionCompleted-7130A235-Result.json.gz", "isCommitted": false, "name": "gBIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 4736, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 9993, "count": 4736}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 11}, {"accountName": "devstoreaccount1", "containerName": "durablefunctionshub-largemessages", "blobName": "328b32a338ab4f25bc1fdd10d14f941e/history-****************-ExecutionCompleted-7130A235-Result.json.gz", "isCommitted": false, "name": "ihIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "size": 10, "persistency": {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "offset": 14729, "count": 10}, "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 12}]}], "idIndex": [2, 4, 5, 6, 7, 8, 9, 10, 11], "binaryIndices": {"accountName": {"name": "accountName", "dirty": false, "values": [2, 8, 7, 6, 5, 4, 3, 1, 0]}, "containerName": {"name": "containerName", "dirty": false, "values": [2, 0, 1, 8, 7, 6, 5, 4, 3]}, "name": {"name": "name", "dirty": false, "values": [4, 5, 3, 7, 8, 6, 1, 2, 0]}, "snapshot": {"name": "snapshot", "dirty": false, "values": [2, 8, 7, 6, 5, 4, 3, 1, 0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$BLOBS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 11, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$BLOCKS_COLLECTION$", "data": [], "idIndex": [], "binaryIndices": {"accountName": {"name": "accountName", "dirty": false, "values": []}, "containerName": {"name": "containerName", "dirty": false, "values": []}, "blobName": {"name": "blobName", "dirty": false, "values": []}, "name": {"name": "name", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$BLOCKS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 12, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}