{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_blob_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "locationId": "<PERSON><PERSON><PERSON>", "path": "cd037e06-0bb0-41c0-acdb-452557ed0841", "size": 17244, "lastModifiedInMS": 1752640644168, "meta": {"revision": 13, "created": 1752640644169, "version": 0, "updated": 1752641575608}, "$loki": 1, "LastModifyInMS": 1752641575608}, {"id": "545f6b12-a38b-4b04-baa3-5cfb07cdfe53", "locationId": "<PERSON><PERSON><PERSON>", "path": "545f6b12-a38b-4b04-baa3-5cfb07cdfe53", "size": 2, "lastModifiedInMS": 1753778291347, "meta": {"revision": 0, "created": 1753778291350, "version": 0}, "$loki": 5}], "idIndex": [1, 5], "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [1, 0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 5, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}