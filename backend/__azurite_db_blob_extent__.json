{"filename": "/home/<USER>/sdlc-agents/backend/__azurite_db_blob_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "cd037e06-0bb0-41c0-acdb-452557ed0841", "locationId": "<PERSON><PERSON><PERSON>", "path": "cd037e06-0bb0-41c0-acdb-452557ed0841", "size": 17244, "lastModifiedInMS": 1752640644168, "meta": {"revision": 13, "created": 1752640644169, "version": 0, "updated": 1752641575608}, "$loki": 1, "LastModifyInMS": 1752641575608}, {"id": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "locationId": "<PERSON><PERSON><PERSON>", "path": "2585cd0d-5239-4f7a-8f5b-ff0357dd943a", "size": 14739, "lastModifiedInMS": 1753780637519, "meta": {"revision": 9, "created": 1753780637520, "version": 0, "updated": 1753781703578}, "$loki": 6, "LastModifyInMS": 1753781703578}], "idIndex": [1, 6], "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [1, 0]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 6, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}