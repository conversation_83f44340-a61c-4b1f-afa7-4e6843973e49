#!/usr/bin/env python3
"""
Test script to verify TCG push to Jira functionality.
This script tests the complete flow from test case creation to linking.
"""

import sys
import os
import logging

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from common.jira.jira_client import JiraClient
from common.jira.jira_request_processor import JiraTestCaseProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_tcg_manual_test_creation():
    """Test creating manual test cases in Jira."""
    
    # Your provided credentials
    base_url = "https://aiordinate.atlassian.net"
    username = "<EMAIL>"
    password = "ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC"
    proj_key = "AA"
    parent_key = "AA-2"  # Parent issue to link test cases to
    
    # Sample test case data (simulating what TCG would generate)
    test_cases = [
        {
            "Summary": "Test Login Functionality",
            "Description": "Verify that users can successfully log into the system",
            "Priority": "High",
            "ManualSteps": [
                {
                    "Step": 1,
                    "Action": "Navigate to login page",
                    "Data": "Valid URL",
                    "ExpectedResult": "Login page displays correctly"
                },
                {
                    "Step": 2,
                    "Action": "Enter valid credentials",
                    "Data": "username: <EMAIL>, password: Test123",
                    "ExpectedResult": "User is logged in successfully"
                }
            ]
        }
    ]
    
    print("=" * 60)
    print("TESTING TCG MANUAL TEST CASE CREATION")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"Project: {proj_key}")
    print(f"Parent Issue: {parent_key}")
    print(f"Test Cases: {len(test_cases)}")
    print("=" * 60)
    
    try:
        # Create JiraTestCaseProcessor with Basic Auth credentials
        auth_data = {'username': username, 'password': password}
        processor = JiraTestCaseProcessor(auth_data, base_url)
        
        print("Processing manual test cases...")
        status_code, response_msg = processor.processing_data_to_jira_manual(
            test_cases, proj_key, parent_key
        )
        
        print("=" * 60)
        print("RESULT:")
        print(f"Status Code: {status_code}")
        print(f"Response Message: {response_msg}")
        print("=" * 60)
        
        if status_code == 200:
            print("✅ SUCCESS: Test cases created successfully!")
            print(f"🔗 Check the parent issue: {base_url}/browse/{parent_key}")
            print("📋 Look for linked test issues in the 'Issue Links' section")
        else:
            print("❌ FAILED: Test case creation was not successful")
            
        return status_code == 200
        
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        logging.error(f"Test failed with exception: {str(e)}", exc_info=True)
        return False

def test_basic_jira_client():
    """Test basic JiraClient functionality."""
    
    base_url = "https://aiordinate.atlassian.net"
    username = "<EMAIL>"
    password = "ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC"
    
    print("\n" + "=" * 60)
    print("TESTING BASIC JIRA CLIENT FUNCTIONALITY")
    print("=" * 60)
    
    try:
        # Create JiraClient instance
        client = JiraClient(base_url, username, password)
        
        # Test creating a simple test issue
        print("Creating a test issue...")
        response = client.create_issue(
            project_key="AA",
            summary="TCG Test - Sample Manual Test",
            description="This is a test issue created by TCG to verify functionality",
            priority="Medium",
            test_type="Manual"
        )
        
        print(f"Create issue response status: {response.status_code}")
        if response.status_code == 201:
            issue_data = response.json()
            new_key = issue_data['key']
            print(f"✅ Test issue created: {new_key}")
            print(f"🔗 View issue: {base_url}/browse/{new_key}")
            
            # Test linking to parent
            print(f"Linking {new_key} to AA-2...")
            link_response = client.link_issues("AA-2", new_key)
            print(f"Link response status: {link_response.status_code}")
            
            if link_response.status_code == 201:
                print("✅ Issue linked successfully!")
            else:
                print(f"❌ Link failed: {link_response.text}")
                
        else:
            print(f"❌ Create issue failed: {response.text}")
            
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        logging.error(f"Basic test failed: {str(e)}", exc_info=True)

if __name__ == "__main__":
    print("🧪 Starting TCG Jira Integration Tests...")
    
    # Test 1: Basic JiraClient functionality
    test_basic_jira_client()
    
    # Test 2: Full TCG manual test case creation
    success = test_tcg_manual_test_creation()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    if success:
        print("✅ All tests passed! TCG push to Jira should work correctly.")
    else:
        print("❌ Some tests failed. Check the logs for details.")
    
    sys.exit(0 if success else 1)
