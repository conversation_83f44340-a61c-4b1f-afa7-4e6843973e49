#!/usr/bin/env python3
"""
Check Jira project configuration to understand available issue types.
"""

import sys
import os
import logging
import json
import requests
import base64

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def check_project_issue_types():
    """Check what issue types are available in the AA project."""
    
    base_url = "https://aiordinate.atlassian.net"
    username = "<EMAIL>"
    password = "ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC"
    
    # Create Basic Auth header
    credentials = f"{username}:{password}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    headers = {
        'Authorization': f'Basic {encoded_credentials}',
        'Content-Type': 'application/json'
    }
    
    print("=" * 60)
    print("CHECKING JIRA PROJECT CONFIGURATION")
    print("=" * 60)
    
    try:
        # Get project details
        project_url = f"{base_url}/rest/api/2/project/AA"
        response = requests.get(project_url, headers=headers, verify=False)
        
        print(f"Project API Status: {response.status_code}")
        if response.status_code == 200:
            project_data = response.json()
            print(f"Project Name: {project_data.get('name', 'N/A')}")
            print(f"Project Key: {project_data.get('key', 'N/A')}")
            
            # Get issue types for this project
            if 'issueTypes' in project_data:
                print("\nAvailable Issue Types:")
                for issue_type in project_data['issueTypes']:
                    print(f"  - {issue_type['name']} (ID: {issue_type['id']})")
            else:
                print("No issue types found in project data")
        else:
            print(f"Failed to get project details: {response.text}")
            
        # Also try the create meta endpoint to see what's available for creation
        print("\n" + "=" * 60)
        print("CHECKING CREATE ISSUE METADATA")
        print("=" * 60)
        
        meta_url = f"{base_url}/rest/api/2/issue/createmeta?projectKeys=AA&expand=projects.issuetypes.fields"
        meta_response = requests.get(meta_url, headers=headers, verify=False)
        
        print(f"Create Meta API Status: {meta_response.status_code}")
        if meta_response.status_code == 200:
            meta_data = meta_response.json()
            
            if 'projects' in meta_data and len(meta_data['projects']) > 0:
                project = meta_data['projects'][0]
                print(f"Project: {project['name']}")
                
                if 'issuetypes' in project:
                    print("\nCreatable Issue Types:")
                    for issue_type in project['issuetypes']:
                        print(f"  - {issue_type['name']} (ID: {issue_type['id']})")
                        
                        # Check if this issue type has the custom fields we need
                        if 'fields' in issue_type:
                            fields = issue_type['fields']
                            print(f"    Available fields: {len(fields)} total")
                            
                            # Look for test-related custom fields
                            for field_id, field_info in fields.items():
                                field_name = field_info.get('name', 'Unknown')
                                if 'test' in field_name.lower() or 'custom' in field_id:
                                    print(f"    - {field_id}: {field_name}")
                else:
                    print("No issue types found in create meta")
            else:
                print("No projects found in create meta")
        else:
            print(f"Failed to get create meta: {meta_response.text}")
            
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        logging.error(f"Check failed: {str(e)}", exc_info=True)

if __name__ == "__main__":
    print("🔍 Checking Jira Project Configuration...")
    check_project_issue_types()
    print("\n" + "=" * 60)
    print("CHECK COMPLETE")
    print("=" * 60)
