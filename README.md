# sdlc-agent

## Overview
sdlc-agent is a full-stack application designed to streamline software development lifecycle processes. The backend integrates with Jira and GPT to provide automation and orchestration, while the frontend offers a user-friendly interface for managing tasks, generating responses, and more.

## Project Structure
```
.env                     # Root environment variables file
.gitignore               # Root gitignore
README.md                # Project documentation
requirements.txt         # Root Python dependencies (if any)

backend/                 # Backend services and logic
    .env.example         # Backend environment variables example
    .gitignore           # Backend gitignore
    README.md            # Backend documentation
    requirements.txt     # Backend dependencies
    agents/              # Agent implementations (ras, tcg)
        ras/
        tcg/
    common/              # Shared backend modules
        __init__.py
        ai_search/
        db/
        jira/
        llm/
        prompts/
        utils/
    DocIngestionFunction/        # Azure Function for document ingestion
        __init__.py
        function.json
    DurableFunctionsHttpStart/   # Azure Durable Functions HTTP starter
    DurableFunctionsOrchestrator/# Azure Durable Functions orchestrator
    ExecuteRAS/                  # RAS execution logic
    ExecuteTCG/                  # TCG execution logic
    PushtoJira/                  # Jira integration logic

frontend/                # Frontend (Streamlit-based) application
    .env                 # Frontend environment variables
    README.md            # Frontend documentation
    requirements.txt     # Frontend dependencies
    src/                 # Frontend source code
        app.py           # Main application entry point
        components/      # UI components and related logic
            buttons.py
            dialogs.py
            jira_auth.py
            rating.py
            response_gpt.py
            response_options.py
            sidebar.py
        services/        # Service-related logic
            jira_client.py
        utils/           # Utility functions and helpers
            css_loader.py
            excel_read.py
            session_manager.py
    static/              # Static assets
        image.jpg
        styles.css

static/                  # Project-level static assets (if any)
    image.jpg
    styles.css
```

## Installation

### Backend
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```
2. Create a virtual environment and activate it:
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate
   ```
3. Install backend dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Frontend
1. Open a new terminal and navigate to the frontend directory:
   ```bash
   cd frontend
   ```
2. Create a virtual environment and activate it:
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate
   ```
3. Install frontend dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Backend
- Start backend services as per the instructions in [backend/README.md](backend/README.md).

### Frontend
1. Run the Streamlit application:
   ```bash
   streamlit run src/app.py
   ```
2. Access the application in your browser at `http://localhost:8501`.

## Features
- **Jira Integration**: Authenticate and interact with Jira tasks.
- **GPT Responses**: Generate AI-driven responses using GPT.
- **Customizable UI**: Modify styles and components easily.
- **Session Management**: Maintain user sessions effectively.
- **Azure Functions**: Backend orchestration and automation.

## Contributing
1. Fork the repository.
2. Create a new branch:
   ```bash
   git checkout -b feature-name
   ```
3. Commit your changes:
   ```bash
   git commit -m "Add feature description"
   ```
4. Push to the branch:
   ```bash
   git push origin feature-name
   ```
5. Open a pull request.

## License
This project is licensed under the MIT License. See the LICENSE file for details.









RequestHandlerAgent / RequestHandlerAgent_jira

Purpose: Handles the initial user request.
Behavior:
If input_type is "text_input", it uses a prompt from the prompt manager for "RequestHandlerAgent".
If input_type is "jira_id", it uses a static prompt (request_handler_agent) and attaches the AzureAISearchClient's semantic_search_ras tool, enabling the agent to perform semantic search in Azure AI Search.
Role: Entry point for processing user input, possibly retrieving context from Azure AI Search for JIRA-based requests.
AnalyserAgent / AnalyserAgent_jira

Purpose: Analyzes the request or context.
Behavior:
If input_type is "text_input", it uses a prompt from the prompt manager for "AnalyserAgent".
If input_type is "jira_id", it uses a static prompt (analyser_prompt).
Role: Provides analysis or breakdown of the request, possibly extracting requirements or summarizing information.
ReviewerAgent

Purpose: Reviews the output from previous agents.
Behavior: Always uses a prompt from the prompt manager for "ReviewerAgent".
Role: Acts as a quality control step, reviewing and possibly critiquing or improving the analyzed content.
FinalResponseGeneratorAgent

Purpose: Generates the final response to the user.
Behavior: Uses a static prompt (final_response_generator_prompt).
Role: Synthesizes all previous outputs into a final, user-facing response.





 data_extractor_agent / data_extractor_prompt_jira
Role: Initial agent in the workflow.
Purpose: Receives the user input and hands it over to the analyser_agent (only once per conversation).
Special (Jira): If using a tool (like Azure AI Search), it tries to find relevant test cases and passes them along with the user input to the analyser_agent. If not relevant, it just passes the user input.
Behavior: After handing over, it remains idle and does not participate further.
2. analyser_agent
Role: Test case generator.
Purpose: Generates test cases (manual or cucumber) from the requirements provided by the request_handler_agent or data_extractor_agent.
Manual Mode: Produces detailed manual test cases with steps, actions, data, and expected results.
Automatic/Cucumber Mode: Produces cucumber-style test cases (Given/When/Then).
Jira Mode: Specializes in banking domain, uses few-shot examples if provided, and handles missing fields gracefully.
Behavior: Sends generated test cases to the reviewer_agent for review.
3. reviewer_agent
Role: Quality reviewer.
Purpose: Reviews the test cases from the analyser_agent for coverage, correctness, clarity, and completeness.
Behavior:
If test cases are not good enough, sends detailed feedback and marks as "FAILED", prompting the analyser_agent to revise and resend.
If test cases are good, marks as "SUCCESS" and passes them to the final_response_generator_agent.
Never generates or modifies test cases itself.
4. final_response_generator_agent
Role: Finalizer.
Purpose: Outputs the final, approved test cases in a strict JSON format, only when the reviewer_agent confirms "SUCCESS".
Behavior: Responds only once per workflow, adds "status": "TERMINATE" to the output, and never generates or modifies test cases itself.
5. team_prompt
Role: Orchestrator (meta-agent).
Purpose: Decides which agent should act next based on the conversation context and workflow rules.








. Azure AI Search
Purpose: To provide fast, semantic search capabilities over your indexed data (such as requirements, test cases, or documentation).
How it’s used:
Agents (like RequestHandlerAgent_jira) use AI Search to retrieve relevant context or examples from a large corpus, especially when handling JIRA-based requests.
The AzureAISearchClient class connects to your AI Search index and performs vector-based or semantic queries to find the most relevant information for the agent’s current task.
Benefit:
Enables your agents to ground their responses in real, indexed data and provide context-aware answers or test cases.
2. Azure Blob Storage
Purpose: To store and retrieve large files, such as CSVs containing requirements, test cases, or other bulk data.
How it’s used:
The BlobFileProcessor class downloads files from Blob Storage for ingestion or processing.
Used in your document ingestion pipeline to load data that will be indexed in AI Search or used by agents.
Benefit:
Provides scalable, durable storage for large datasets that are too big or too unstructured for a database.

3.PostgreSQL
Purpose: To store structured data such as prompts, agent configurations, and possibly user or workflow metadata.
How it’s used:
The PromptManager and related code are designed to fetch prompts and configuration from PostgreSQL, allowing dynamic updates without code changes.
Some prompts are still hardcoded for testing, but the intent is to move to database-driven configuration.
Benefit:
Centralizes and manages all agent prompts and settings, making it easy to update agent behavior or add new workflows without redeploying code
