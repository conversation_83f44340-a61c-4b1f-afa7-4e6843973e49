#!/usr/bin/env python3
"""
Test script to verify Jira push functionality with the provided credentials.
This script tests the fixed JiraClient implementation.
"""

import sys
import os
import logging

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from common.jira.jira_client import JiraClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_jira_update():
    """Test the Jira update functionality with provided credentials."""
    
    # Your provided credentials
    base_url = "https://aiordinate.atlassian.net"
    username = "<EMAIL>"
    password = "ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC"
    jira_id = "AA-1"
    
    # Test data
    test_title = "RAS Test - Updated Title"
    test_description = "**Updated Description from RAS Helper**\n\nThis is a test update to verify the Jira push functionality is working correctly.\n\n**Key Features:**\n- Basic Auth implementation\n- Proper error handling\n- Detailed logging"
    test_acceptance_criteria = "- Given the RAS helper generates content, when push to Jira is clicked, then the content should be successfully updated in Jira\n- Given proper credentials, when API call is made, then it should return status 204\n- Given acceptance criteria, when provided, then it should be added as a comment"
    
    print("=" * 60)
    print("TESTING JIRA UPDATE FUNCTIONALITY")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"Username: {username}")
    print(f"Issue ID: {jira_id}")
    print(f"Title: {test_title}")
    print(f"Description length: {len(test_description)}")
    print(f"Acceptance criteria length: {len(test_acceptance_criteria)}")
    print("=" * 60)
    
    try:
        # Create JiraClient instance
        print("Creating JiraClient instance...")
        client = JiraClient(base_url, username, password)
        
        # Test the update
        print("Calling update_jira_issue...")
        status_code, response_msg = client.update_jira_issue(
            jira_id, 
            test_title, 
            test_description, 
            test_acceptance_criteria
        )
        
        print("=" * 60)
        print("RESULT:")
        print(f"Status Code: {status_code}")
        print(f"Response Message: {response_msg}")
        print("=" * 60)
        
        if status_code == 204:
            print("✅ SUCCESS: Jira issue updated successfully!")
            print(f"🔗 View the updated issue: {base_url}/browse/{jira_id}")
        else:
            print("❌ FAILED: Jira update was not successful")
            
        return status_code == 204
        
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        logging.error(f"Test failed with exception: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = test_jira_update()
    sys.exit(0 if success else 1)
