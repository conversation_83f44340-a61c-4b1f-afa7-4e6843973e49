#!/usr/bin/env python3
"""
Check available issue link types in Jira.
"""

import sys
import os
import logging
import json
import requests
import base64

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def check_link_types():
    """Check what issue link types are available."""
    
    base_url = "https://aiordinate.atlassian.net"
    username = "<EMAIL>"
    password = "ATATT3xFfGF0eUpOAJhpvv_-oYxDWPQTbhoEls6JDB4WlaDb1QW-Zc2eEazdzJUk9lm8mWk57hYSKGFP7iEX5qe4KX0QdLibDSBunSCH1N1pXroSwav_e-nqj1quOV2K0A6ou2soO0SythYnDl9sTP1abIFzRNhFgszAHqrVA8aYrlpsARPXRhk=3EC06FDC"
    
    # Create Basic Auth header
    credentials = f"{username}:{password}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    headers = {
        'Authorization': f'Basic {encoded_credentials}',
        'Content-Type': 'application/json'
    }
    
    print("=" * 60)
    print("CHECKING JIRA ISSUE LINK TYPES")
    print("=" * 60)
    
    try:
        # Get issue link types
        link_types_url = f"{base_url}/rest/api/2/issueLinkType"
        response = requests.get(link_types_url, headers=headers, verify=False)
        
        print(f"Link Types API Status: {response.status_code}")
        if response.status_code == 200:
            link_data = response.json()
            
            if 'issueLinkTypes' in link_data:
                print("\nAvailable Issue Link Types:")
                for link_type in link_data['issueLinkTypes']:
                    print(f"  - Name: {link_type['name']}")
                    print(f"    Inward: {link_type['inward']}")
                    print(f"    Outward: {link_type['outward']}")
                    print()
            else:
                print("No issue link types found")
        else:
            print(f"Failed to get link types: {response.text}")
            
    except Exception as e:
        print(f"❌ EXCEPTION: {str(e)}")
        logging.error(f"Check failed: {str(e)}", exc_info=True)

if __name__ == "__main__":
    print("🔍 Checking Jira Issue Link Types...")
    check_link_types()
    print("\n" + "=" * 60)
    print("CHECK COMPLETE")
    print("=" * 60)
