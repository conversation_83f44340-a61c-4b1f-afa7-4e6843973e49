import streamlit as st
import uuid

def initialize_session_state():
    default_stats = {
        "selected_helper" : None,
        "selected_input_type" : None,
        "feedback_key": str(uuid.uuid4()),
        "welcome_message": <PERSON>alse,
        "file_upload_push_to_jira": <PERSON>als<PERSON>,
        "yes_clicked": <PERSON>als<PERSON>,
        "no_clicked": <PERSON>als<PERSON>,
        "jira_auth_popup_actioned": <PERSON>als<PERSON>,
        "jira_user_authenticated": <PERSON>als<PERSON>,
        "jira_display_count": 200,
        "jira_ids_max_results": 200,
        "free_text_input": None,
        "jira_selected": [],
        "update_approved": {},
        "rewritten_content": {},
        "pushed_status": {},
        "user_confirmed": False,
        "logged_in_user": None,
        "response_duration": 0,
        "invalid_len_input": False,
        # New authentication variables for Basic Auth
        "jira_username": None,
        "jira_password": None,
        "jira_session": None  # Keep for backward compatibility
    }

    for key, value in default_stats.items():
        if key not in st.session_state:
            st.session_state[key] = value